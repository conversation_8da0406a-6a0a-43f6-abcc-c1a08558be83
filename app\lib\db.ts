import Database from "better-sqlite3"
import path from "path"

const dbPath = path.join(process.cwd(), "data", "tokens.db")
const db = new Database(dbPath)

// Initialize database
db.exec(`
  CREATE TABLE IF NOT EXISTS tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    url_path TEXT NOT NULL,
    api_path TEXT NOT NULL,
    token TEXT NOT NULL,
    model_list TEXT NOT NULL,
    note TEXT,
    user_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`)

export interface TokenRecord {
  id?: number
  name: string
  urlPath: string
  apiPath: string
  token: string
  modelList: string[]
  note: string
  userId?: string
}

export class TokenService {
  static getAllTokens(userId: string): TokenRecord[] {
    const stmt = db.prepare("SELECT * FROM tokens WHERE user_id = ? ORDER BY created_at DESC")
    const rows = stmt.all(userId) as any[]

    return rows.map((row) => ({
      id: row.id,
      name: row.name,
      urlPath: row.url_path,
      apiPath: row.api_path,
      token: row.token,
      modelList: JSON.parse(row.model_list),
      note: row.note,
    }))
  }

  static createToken(token: TokenRecord, userId: string): number {
    const stmt = db.prepare(`
      INSERT INTO tokens (name, url_path, api_path, token, model_list, note, user_id)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      token.name,
      token.urlPath,
      token.apiPath,
      token.token,
      JSON.stringify(token.modelList),
      token.note,
      userId,
    )

    return result.lastInsertRowid as number
  }

  static updateToken(id: number, token: TokenRecord, userId: string): boolean {
    const stmt = db.prepare(`
      UPDATE tokens 
      SET name = ?, url_path = ?, api_path = ?, token = ?, model_list = ?, note = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
    `)

    const result = stmt.run(
      token.name,
      token.urlPath,
      token.apiPath,
      token.token,
      JSON.stringify(token.modelList),
      token.note,
      id,
      userId,
    )

    return result.changes > 0
  }

  static deleteToken(id: number, userId: string): boolean {
    const stmt = db.prepare("DELETE FROM tokens WHERE id = ? AND user_id = ?")
    const result = stmt.run(id, userId)
    return result.changes > 0
  }
}

export default db
