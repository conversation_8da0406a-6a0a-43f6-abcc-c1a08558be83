# Token Library

一个简单的 NextJS 应用，用于管理 API tokens，使用 Keycloak 进行身份验证。

## 功能特性

- 🔐 Keycloak 身份验证 (PKCE S256)
- 💾 嵌入式 SQLite 数据库
- 🔍 全字段搜索功能
- ✨ 简约现代的 UI 设计
- 📱 响应式设计

## Token 记录字段

每个 token 记录包含以下字段：
- **名字** - Token 的显示名称
- **URL Path** - 相关的 URL 路径
- **API Path** - API 端点路径
- **Token** - 实际的 token 值
- **Model List** - 支持的模型列表（数组）
- **Note** - 备注信息

## 环境配置

应用使用以下环境变量（已在 `.env.local` 中配置）：

```env
# Keycloak Configuration
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_CLIENT_ID=token-library
KEYCLOAK_CLIENT_SECRET=SuEX41rjIcqjRayh7TK7dB69EMGbjZO4

# Application Configuration
PORT=9032
```

## 运行应用

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 访问应用：
```
http://localhost:9032
```

## 数据库

应用使用 SQLite 数据库，数据文件存储在 `./data/tokens.db`。数据库会在首次运行时自动创建。

## 认证流程

1. 用户点击登录按钮
2. 重定向到 Keycloak 认证页面
3. 用户在 Keycloak 中完成认证
4. 回调到应用并设置认证 cookies
5. 用户可以开始管理 tokens

## API 端点

- `GET /api/tokens` - 获取所有 tokens（支持搜索参数）
- `POST /api/tokens` - 创建新 token
- `PUT /api/tokens/[id]` - 更新 token
- `DELETE /api/tokens/[id]` - 删除 token
- `GET /api/auth/login` - 开始登录流程
- `GET /api/auth/callback` - 处理 Keycloak 回调
- `POST /api/auth/logout` - 登出
- `GET /api/auth/me` - 获取当前用户信息

## 技术栈

- **Frontend**: Next.js 14, React, TypeScript
- **UI**: Tailwind CSS, Radix UI
- **Database**: SQLite (better-sqlite3)
- **Authentication**: Keycloak (OIDC)
- **Icons**: Lucide React
