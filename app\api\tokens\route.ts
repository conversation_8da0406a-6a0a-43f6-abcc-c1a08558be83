import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { TokenService } from "@/app/lib/db"

async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function GET() {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const tokens = TokenService.getAllTokens(user.id)
    return NextResponse.json(tokens)
  } catch (error) {
    console.error("Failed to fetch tokens:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const tokenData = await request.json()
    const id = TokenService.createToken(tokenData, user.id)
    return NextResponse.json({ id, success: true })
  } catch (error) {
    console.error("Failed to create token:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
