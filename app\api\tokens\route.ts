import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { TokenService } from "@/app/lib/db"

async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function GET(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')

    const tokens = TokenService.getAllTokens(user.id, search || undefined)
    return NextResponse.json(tokens)
  } catch (error) {
    console.error("Failed to fetch tokens:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const tokenData = await request.json()

    if (!tokenData.name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 })
    }

    const name = TokenService.createToken(tokenData, user.id)
    return NextResponse.json({ name, success: true })
  } catch (error) {
    console.error("Failed to create token:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
