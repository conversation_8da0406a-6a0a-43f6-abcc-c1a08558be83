"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Plus, Search, Edit, Trash2, LogOut } from "lucide-react";
import { useAuth } from "./lib/auth-context";

interface TokenRecord {
  id?: number;
  name: string;
  urlPath: string;
  apiPath: string;
  token: string;
  modelList: string[];
  note: string;
}

export default function HomePage() {
  const { user, logout } = useAuth();
  const [tokens, setTokens] = useState<TokenRecord[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentToken, setCurrentToken] = useState<TokenRecord>({
    name: "",
    urlPath: "",
    apiPath: "",
    token: "",
    modelList: [],
    note: "",
  });

  useEffect(() => {
    if (user) {
      fetchTokens();
    }
  }, [user]);

  const fetchTokens = async (search?: string) => {
    try {
      const url = search
        ? `/api/tokens?search=${encodeURIComponent(search)}`
        : "/api/tokens";
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setTokens(data);
      }
    } catch (error) {
      console.error("Failed to fetch tokens:", error);
    }
  };

  const handleSave = async () => {
    try {
      const url = currentToken.id
        ? `/api/tokens/${currentToken.id}`
        : "/api/tokens";
      const method = currentToken.id ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(currentToken),
      });

      if (response.ok) {
        fetchTokens();
        setIsAddDialogOpen(false);
        setIsEditDialogOpen(false);
        setCurrentToken({
          name: "",
          urlPath: "",
          apiPath: "",
          token: "",
          modelList: [],
          note: "",
        });
      }
    } catch (error) {
      console.error("Failed to save token:", error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await fetch(`/api/tokens/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchTokens();
      }
    } catch (error) {
      console.error("Failed to delete token:", error);
    }
  };

  const handleEdit = (token: TokenRecord) => {
    setCurrentToken(token);
    setIsEditDialogOpen(true);
  };

  const handleAdd = () => {
    setCurrentToken({
      name: "",
      urlPath: "",
      apiPath: "",
      token: "",
      modelList: [],
      note: "",
    });
    setIsAddDialogOpen(true);
  };

  // Use debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (user) {
        fetchTokens(searchTerm || undefined);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, user]);

  const filteredTokens = tokens; // Server-side filtering

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Token Manager</CardTitle>
            <CardDescription>Please log in to continue</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className="w-full"
              onClick={() => (window.location.href = "/api/auth/login")}
            >
              Login with Keycloak
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-semibold text-gray-900">
              Token Manager
            </h1>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                Welcome, {user.name}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={logout}
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search tokens..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-80"
              />
            </div>
          </div>
          <Button onClick={handleAdd}>
            <Plus className="w-4 h-4 mr-2" />
            Add Token
          </Button>
        </div>

        <div className="grid gap-4">
          {filteredTokens.map((token) => (
            <Card
              key={token.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center gap-4">
                      <h3 className="text-lg font-semibold">{token.name}</h3>
                      <div className="flex gap-2">
                        {token.modelList.map((model, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                          >
                            {model}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">
                          URL Path:
                        </span>
                        <p className="text-gray-900">{token.urlPath}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">
                          API Path:
                        </span>
                        <p className="text-gray-900">{token.apiPath}</p>
                      </div>
                    </div>

                    <div className="text-sm">
                      <span className="font-medium text-gray-600">Token:</span>
                      <p className="text-gray-900 font-mono text-xs bg-gray-100 p-2 rounded mt-1">
                        {token.token.substring(0, 20)}...
                      </p>
                    </div>

                    {token.note && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-600">Note:</span>
                        <p className="text-gray-900">{token.note}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(token)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(token.id!)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTokens.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No tokens found</p>
          </div>
        )}
      </main>

      {/* Add/Edit Dialog */}
      <Dialog
        open={isAddDialogOpen || isEditDialogOpen}
        onOpenChange={(open) => {
          setIsAddDialogOpen(false);
          setIsEditDialogOpen(false);
        }}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {currentToken.id ? "Edit Token" : "Add New Token"}
            </DialogTitle>
            <DialogDescription>
              {currentToken.id
                ? "Update the token information"
                : "Create a new token record"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={currentToken.name}
                  onChange={(e) =>
                    setCurrentToken({ ...currentToken, name: e.target.value })
                  }
                />
              </div>
              <div>
                <Label htmlFor="urlPath">URL Path</Label>
                <Input
                  id="urlPath"
                  value={currentToken.urlPath}
                  onChange={(e) =>
                    setCurrentToken({
                      ...currentToken,
                      urlPath: e.target.value,
                    })
                  }
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="apiPath">API Path</Label>
                <Input
                  id="apiPath"
                  value={currentToken.apiPath}
                  onChange={(e) =>
                    setCurrentToken({
                      ...currentToken,
                      apiPath: e.target.value,
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="modelList">Model List (comma separated)</Label>
                <Input
                  id="modelList"
                  value={currentToken.modelList.join(", ")}
                  onChange={(e) =>
                    setCurrentToken({
                      ...currentToken,
                      modelList: e.target.value
                        .split(",")
                        .map((s) => s.trim())
                        .filter((s) => s),
                    })
                  }
                />
              </div>
            </div>

            <div>
              <Label htmlFor="token">Token</Label>
              <Textarea
                id="token"
                value={currentToken.token}
                onChange={(e) =>
                  setCurrentToken({ ...currentToken, token: e.target.value })
                }
                className="font-mono text-sm"
              />
            </div>

            <div>
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={currentToken.note}
                onChange={(e) =>
                  setCurrentToken({ ...currentToken, note: e.target.value })
                }
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddDialogOpen(false);
                setIsEditDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {currentToken.id ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
